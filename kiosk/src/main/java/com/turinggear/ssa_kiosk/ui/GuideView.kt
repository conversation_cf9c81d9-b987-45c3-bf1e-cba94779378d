package com.turinggear.ssa_kiosk.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.Train
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.compose.rememberAsyncImagePainter
import com.turinggear.ssa_kiosk.*
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.IsPortrait
import kotlinx.coroutines.delay
import com.turinggear.ssa_shared.R

@Composable
fun GuideViewLandscape() {
    Column(
        modifier = Modifier
            .fillMaxHeight()
            .fillMaxWidth(fraction = 0.5f),
        horizontalAlignment = Alignment.Start
    ) {
        GuideMapView(
            Modifier
                .padding(top = MARGIN.dp)
                .padding(horizontal = MARGIN.dp)
        )
        GuideNoticeView(
            Modifier
                .weight(1f)
                .padding(horizontal = (MARGIN + 4).dp),
            title = "乘坐须知",
            content = GLOBAL_POLL?.data?.machineStaticConfig?.screen_passenger_rules ?: DEFAULT_RULES_PASSENGER ?: "",
            enableScroll = true
        )
    }
}

@Composable
fun GuideViewPortrait() {
    Row(
        Modifier
            .fillMaxWidth()
            .fillMaxHeight(fraction = 0.3f)
            .padding(horizontal = 16.dp)
            .padding(vertical = 16.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        val enableLayoutYqh = LocalContext.current.resources.getBoolean(R.bool.enable_layout_yqh)
        var weightForMapView = 1f

        if (enableLayoutYqh) {
            weightForMapView = 2f
        } else {
            weightForMapView = 1f

            GuideNoticeView(
                Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(end = 8.dp)
                    .border(
                        0.5.dp,
                        MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                        RoundedCornerShape(8.dp)
                    )
                    .padding(start = 8.dp, end = 8.dp),
                content = GLOBAL_POLL?.data?.machineStaticConfig?.screen_passenger_rules
                    ?: DEFAULT_RULES_PASSENGER ?: "",
                title = "乘坐须知",
            )
        }
        GuideMapView(Modifier.padding(horizontal = 5.dp).weight(weightForMapView))
        CurrentStationView(
            Modifier
                .weight(1f)
                .padding(start = 8.dp)
                .fillMaxHeight()
                .background(Color.Transparent)
        )
    }
}

@Composable
fun GuideMapView(modifier: Modifier = Modifier, aspectRatio: Float = 1f) {
    var url = GLOBAL_POLL?.data?.machineStaticConfig?.map1_url ?: URL_MAP_1
    var placeholder = R.drawable.map_1
    var switchMap by remember { mutableStateOf(false) }
    if (switchMap) {
        url = GLOBAL_POLL?.data?.machineStaticConfig?.map2_url ?: URL_MAP_2
        placeholder = R.drawable.wx_qrcode
    }
//    url = ""
//    placeholder = R.drawable.map_test
    AsyncImage(
        model = url,
        placeholder = rememberAsyncImagePainter(placeholder),
        error = rememberAsyncImagePainter(placeholder),
        fallback = rememberAsyncImagePainter(placeholder),
        contentDescription = null,
        contentScale = ContentScale.Crop,
        modifier = modifier
            .border(
                0.5.dp,
                MaterialTheme.colors.onSurface.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
//            .background(MaterialTheme.colors.onSurface.copy(alpha = 0.1f))
//            .aspectRatio(aspectRatio)
            .clickable {
                switchMap = !switchMap
            }
    )
}

@Composable
fun GuideNoticeView(
    modifier: Modifier = Modifier,
    content: String,
    title: String = "乘坐须知",
    enableScroll: Boolean = true,
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Top
        ) {
            Row(
                modifier = Modifier
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Icon(
                    Icons.Outlined.Train,
                    contentDescription = null,
                    modifier = Modifier
                        .size(24.dp)
                        .padding(top = 2.dp, end = 4.dp),
                    tint = MaterialTheme.colors.onBackground.copy(0.8f)
                )
                Text(
                    text = title,
                    color = MaterialTheme.colors.onBackground.copy(0.8f),
                    style = MaterialTheme.typography.button
                )
            }

            val scrollState = rememberScrollState()
            Text(
                text = content,
                modifier = Modifier
                    .padding(bottom = 8.dp)
                    .let { if (enableScroll) it.verticalScroll(scrollState) else it },
                color = MaterialTheme.colors.onBackground.copy(0.8f),
                fontSize = if (IsPortrait) 8.sp else 9.sp,
                lineHeight = if (IsPortrait) 16.sp else 15.sp
            )

            if (enableScroll) {
                LaunchedEffect(true) {
                    while (true) {
                        delay(1000)
                        if ((scrollState.maxValue > 0) and !scrollState.isScrollInProgress) {
                            if (scrollState.value < scrollState.maxValue) {
                                delay(2000)
                                scrollState.animateScrollTo(
                                    scrollState.maxValue,
                                    animationSpec = tween(
                                        scrollState.maxValue * 100,
                                        easing = LinearEasing
                                    )
                                )
                            }
                            if (scrollState.value == scrollState.maxValue) {
                                delay(2000)
                                scrollState.animateScrollTo(
                                    0,
                                    animationSpec = tween(
                                        scrollState.maxValue * 10,
                                        easing = LinearEasing
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable(enabled = false, onClick = {})
        )
    }
}

@Composable
fun YqhGuideView(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier.fillMaxSize().padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        GuideNoticeView(
            modifier = Modifier.weight(1f),
            content = GLOBAL_POLL?.data?.machineStaticConfig?.screen_passenger_rules ?: DEFAULT_RULES_PASSENGER ?: "",
            title = "小火车乘坐须知",
            enableScroll = false
        )
        GuideNoticeView(
            modifier = Modifier.weight(1f),
            content = GLOBAL_POLL?.data?.machineStaticConfig?.screen_passenger_rules ?: DEFAULT_RULES_PASSENGER ?: "",
            title = "电瓶车乘坐须知",
            enableScroll = false
        )
    }
}