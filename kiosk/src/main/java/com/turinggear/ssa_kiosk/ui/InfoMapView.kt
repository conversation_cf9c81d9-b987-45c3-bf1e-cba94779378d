package com.turinggear.ssa_kiosk.ui

import android.util.Log
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import coil.compose.rememberAsyncImagePainter
import com.turinggear.ssa_kiosk.viewmodel.StationViewModel
import com.turinggear.ssa_shared.GLOBAL_POLL
import com.turinggear.ssa_shared.R

@Composable
fun InfoMap3LandscapeView(modifier: Modifier = Modifier) {
    val stationViewModel: StationViewModel = viewModel()
    val routeImagesFlow = stationViewModel.routeImagesFlow.collectAsState()

    // 默认
    var url = GLOBAL_POLL?.data?.machineStaticConfig?.map3_url ?: ""
    // 如果有值则覆盖
    routeImagesFlow.value?.get(0)?.let { url = it }
    routeImagesFlow.value?.toString()?.let { Log.e("XXX", "routes" + it) }

//    Log.e("XXX", "routes url")
//    Log.e("XXX", "routes " + url)
    val placeholder = R.drawable.wx_qrcode
    AsyncImage(
        model = url,
        placeholder = rememberAsyncImagePainter(placeholder),
        error = rememberAsyncImagePainter(placeholder),
        fallback = rememberAsyncImagePainter(placeholder),
        contentDescription = null,
        contentScale = ContentScale.Crop,
        modifier = modifier.fillMaxSize()
    )
}

@Composable
fun InfoMap4PortraitView(modifier: Modifier = Modifier) {
    val placeholder = R.drawable.wx_qrcode

    val stationViewModel: StationViewModel = viewModel()
    val routeImagesFlow = stationViewModel.routeImagesFlow.collectAsState()

    // 默认
    var url = GLOBAL_POLL?.data?.machineStaticConfig?.map4_url ?: ""
    // 如果有值则覆盖
    routeImagesFlow.value?.get(0)?.let { url = it }
    routeImagesFlow.value?.toString()?.let { Log.e("XXX", "routes" + it) }

//    url = "https://ssa-static-shiyuan.mwebpage.com/images/map_test.png"

//    Log.e("XXX", "routes url")
    Log.e("XXX", "routes " + url)

    AsyncImage(
        model = url,
        placeholder = rememberAsyncImagePainter(placeholder),
        error = rememberAsyncImagePainter(placeholder),
        fallback = rememberAsyncImagePainter(placeholder),
        contentDescription = null,
        contentScale = ContentScale.Crop,
        modifier = modifier.fillMaxSize().padding(16.dp)
    )
}